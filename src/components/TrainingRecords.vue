<script setup lang="ts">
import { closeToast, showFailToast, showImagePreview, showLoadingToast } from 'vant'
import { defineProps, nextTick, onMounted, ref } from 'vue'
import { V1ManageTrainStudyRecordsRecordDetailAlarmUrlRecordDetailAlarmId } from '@/api/api.req'

// 定义训练记录数据接口
interface TrainingRecord {
  date: string
  workPassRate: string
  workCount: string
  workFailCount: string
  actionRate: string
  actionFailCount: string
  images?: {
    imageUrl: string
    description: string
    time: string
    iconImg: string
    isQualified?: boolean // 是否合格标识
    recordDetailAlarmId?: string // 图片ID，用于懒加载
    loaded?: boolean // 是否已加载
  }[]
  scrollLeft?: number
  activeImgIndex?: number
  visibleImageRange?: [number, number] // 可见图片范围 [startIndex, endIndex]
}

// 定义组件属性
const props = defineProps({
  // 标题
  title: {
    type: String,
    default: '训练记录',
  },
  // 训练记录数据
  records: {
    type: Array as () => TrainingRecord[],
    default: () => [],
  },
})

// 滚动视图引用
const recordScrollViewRefs = ref<HTMLElement[]>([])
function setRecordScrollViewRef(el: any, index: number) {
  if (el) {
    recordScrollViewRefs.value[index] = el as HTMLElement
    // 初始化可见范围
    initVisibleRange(index)
  }
}

// 初始化可见图片范围
function initVisibleRange(recordIndex: number) {
  const record = props.records[recordIndex]
  if (!record || !record.images || record.images.length === 0)
    return

  // 默认第一张图片可见
  record.visibleImageRange = [0, Math.min(1, record.images.length - 1)]
  // 预加载第一张图片
  loadImageIfNeeded(record, 0)
}

// 处理滚动事件
function handleScroll(event: Event, recordIndex: number) {
  const record = props.records[recordIndex]
  if (!record || !record.images || record.images.length <= 1)
    return

  // 获取当前滚动位置 - Web环境下使用scrollLeft
  const target = event.target as HTMLElement
  const scrollLeft = target.scrollLeft
  const imageWidth = 240 // px
  const gap = 12 // mr-3的gap，单位px

  // 计算当前显示的图片索引
  const currentIndex = Math.round(scrollLeft / (imageWidth + gap))
  // 更新activeImgIndex
  if (record.activeImgIndex !== currentIndex + 1) {
    record.activeImgIndex = currentIndex + 1
  }

  // 更新可见图片范围 (当前图片及其前后各一张)
  const startIndex = Math.max(0, currentIndex - 1)
  const endIndex = Math.min(record.images.length - 1, currentIndex + 1)

  // 如果可见范围发生变化，才更新并触发加载
  if (!record.visibleImageRange
    || record.visibleImageRange[0] !== startIndex
    || record.visibleImageRange[1] !== endIndex) {
    // console.log(`记录${recordIndex}可见范围更新: ${startIndex}-${endIndex}`)
    record.visibleImageRange = [startIndex, endIndex]

    // 加载可见范围内的图片
    for (let i = startIndex; i <= endIndex; i++) {
      loadImageIfNeeded(record, i)
    }
  }
}

// 判断图片是否在可视范围内
function isImageVisible(record: TrainingRecord, imgIndex: number) {
  if (!record.visibleImageRange)
    return false
  const [start, end] = record.visibleImageRange
  return imgIndex >= start && imgIndex <= end
}

// 获取图片URL
function getImageUrl(record: TrainingRecord, imgIndex: number) {
  const img = record.images?.[imgIndex]
  if (!img)
    return '/static/def-bg.png'

  // 如果已经加载过，直接返回URL
  if (img.loaded && img.imageUrl) {
    return img.imageUrl
  }

  // 否则返回默认图片
  return '/static/def-bg.png'
}

// 单独的图片加载函数，返回Promise以便await
async function loadImageAsync(img: NonNullable<TrainingRecord['images']>[0]) {
  if (!img || img.loaded)
    return img?.imageUrl || '/static/def-bg.png'

  const defImageUrl = '/static/def-bg.png'
  // 如果有recordDetailAlarmId但没有加载过图片
  if (img.recordDetailAlarmId && !img.loaded) {
    try {
      // console.log(`加载图片ID: ${img.recordDetailAlarmId}`) // 调试信息
      const imageResponse = await V1ManageTrainStudyRecordsRecordDetailAlarmUrlRecordDetailAlarmId({
        recordDetailAlarmId: img.recordDetailAlarmId,
      })
      img.imageUrl = imageResponse || defImageUrl
      img.loaded = true
      // console.log(`图片加载成功: ${img.imageUrl}`) // 调试信息
      return img.imageUrl
    }
    catch {
      // console.error(`图片加载失败:`, error)
      img.imageUrl = defImageUrl
      img.loaded = true
      return defImageUrl
    }
  }
  else if (!img.recordDetailAlarmId) {
    // 没有ID的情况，标记为已加载，避免重复请求
    img.loaded = true
    return img.imageUrl || defImageUrl
  }
  return defImageUrl
}

// 按需加载图片
async function loadImageIfNeeded(record: TrainingRecord, imgIndex: number) {
  const img = record.images?.[imgIndex]
  if (!img || img.loaded)
    return
  const defImageUrl = '/static/def-bg.png'
  // 如果有recordDetailAlarmId但没有加载过图片
  if (img.recordDetailAlarmId && !img.loaded) {
    try {
      console.log(`加载图片: ${imgIndex}`) // 调试信息
      const imageResponse = await V1ManageTrainStudyRecordsRecordDetailAlarmUrlRecordDetailAlarmId({
        recordDetailAlarmId: img.recordDetailAlarmId,
      })
      img.imageUrl = imageResponse || defImageUrl
      img.loaded = true
      console.log(`图片${imgIndex}加载成功`) // 调试信息
    }
    catch (error) {
      console.error(`图片${imgIndex}加载失败:`, error)
      img.imageUrl = defImageUrl
      img.loaded = true
    }
  }
  else if (!img.recordDetailAlarmId) {
    // 没有ID的情况，标记为已加载，避免重复请求
    img.loaded = true
  }
}

// 处理指示点点击
function handleIndicatorClick(recordIndex: number, imgIndex: number) {
  const record = props.records[recordIndex]
  if (!record || !record.images || !record.images.length)
    return
  // 单张图片无需滚动
  if (record.images.length <= 1)
    return
  // 计算scrollLeft
  const imageWidth = 240 // px
  const gap = 12 // mr-3的gap，单位px
  const scrollLeft = imgIndex * (imageWidth + gap)
  record.scrollLeft = scrollLeft
  record.activeImgIndex = imgIndex + 1

  // 更新可见范围并加载图片
  const startIndex = Math.max(0, imgIndex - 1)
  const endIndex = Math.min(record.images.length - 1, imgIndex + 1)
  record.visibleImageRange = [startIndex, endIndex]

  // 加载可见范围内的图片
  for (let i = startIndex; i <= endIndex; i++) {
    loadImageIfNeeded(record, i)
  }

  // 滚动到对应图片
  nextTick(() => {
    const scrollView = recordScrollViewRefs.value[recordIndex]
    if (scrollView) {
      scrollView.scrollLeft = scrollLeft
    }
  })
}

// 预览图片
async function previewImages(images: NonNullable<TrainingRecord['images']>, currentIndex: number) {
  // 显示加载中提示
  showLoadingToast({
    message: '加载图片中...',
    forbidClick: true,
  })

  try {
    // 预加载所有图片，确保在预览时所有图片都已加载
    const loadPromises = []

    // 收集所有需要加载的图片的Promise
    for (let i = 0; i < images.length; i++) {
      const img = images[i]
      if (img && img.recordDetailAlarmId && !img.loaded) {
        loadPromises.push(loadImageAsync(img))
      }
    }

    // 等待所有图片加载完成
    if (loadPromises.length > 0) {
      await Promise.all(loadPromises)
    }
    console.log('images', images)

    // 隐藏加载提示
    closeToast()

    // 创建图片URL数组用于预览
    const imageUrls = images.map(img => img.imageUrl || '/static/def-bg.png')

    // 使用Vant的ImagePreview组件预览图片
    showImagePreview({
      images: imageUrls,
      startPosition: currentIndex,
      closeable: true,
      showIndex: true,
      showIndicators: true,
      swipeDuration: 300,
    })
  }
  catch (error) {
    console.error('预览图片失败:', error)
    closeToast()
    showFailToast('图片加载失败')
  }
}

// 组件挂载时初始化所有记录的可见范围
onMounted(() => {
  if (props.records && props.records.length > 0) {
    console.log('初始化训练记录组件，记录数:', props.records.length)
    props.records.forEach((record, index) => {
      if (record.images && record.images.length > 0) {
        // 初始化可见范围为前两张图片
        record.visibleImageRange = [0, Math.min(1, record.images.length - 1)]
        record.activeImgIndex = 1 // 默认选中第一张

        // 确保可见范围内的图片已加载
        for (let i = 0; i <= Math.min(1, record.images.length - 1); i++) {
          loadImageIfNeeded(record, i)
        }

        console.log(`记录${index}初始化完成，图片数:${record.images.length}`)
      }
    })
  }
})
</script>

<template>
  <div class="records-section mb-8 px-0 py-2">
    <div class="section-title mb-2 px-4 text-[36rpx] text-[#333333] font-medium">
      {{ title }}
    </div>

    <!-- 训练记录列表 -->
    <div class="record-list">
      <!-- 记录项 -->
      <div v-for="(record, index) in records" :key="index" class="record-item relative ml-4 mr-4 bg-white">
        <!-- 记录内容 -->
        <div class="record-content relative py-4 pl-8 pr-4">
          <!-- 左侧线条和圆点 -->
          <div class="record-left-border" />
          <div class="record-dot" :class="index === 0 ? 'record-dot-active' : 'record-dot-inactive'" />

          <!-- 日期行 -->
          <div class="mb-1 flex justify-between">
            <div class="flex items-center">
              <span
                class="text-sm font-medium"
                :class="index === 0 ? 'text-[#00996B]' : 'text-[#333333]'"
              >
                {{ record.date }}
              </span>
            </div>
            <div>
              <span class="text-sm text-[#999999]">
                作业次数：
              </span>
              <span class="text-sm text-[#00996B] font-medium">
                {{ record.workCount }}次
              </span>
            </div>
          </div>

          <!-- 数据统计网格 -->
          <div class="mb-1">
            <div class="mb-1 flex justify-between">
              <div class="flex">
                <span class="text-xs text-[#999999]">
                  作业合格率：
                </span>
                <span class="text-xs text-[#333333] font-medium">
                  {{ record.workPassRate }}%
                </span>
              </div>
              <div class="flex">
                <span class="text-xs text-[#999999]">
                  作业不合格次数：
                </span>
                <span class="text-xs text-[#F53F3F] font-medium">
                  {{ record.workFailCount }}次
                </span>
              </div>
            </div>
            <div class="flex justify-between">
              <div class="flex">
                <span class="text-xs text-[#999999]">
                  动作达标率：
                </span>
                <span class="text-xs text-[#333333] font-medium">
                  {{ record.actionRate }}%
                </span>
              </div>
              <div class="flex">
                <span class="text-xs text-[#999999]">
                  动作不达标次数：
                </span>
                <span class="text-xs text-[#F53F3F] font-medium">
                  {{ record.actionFailCount }}次
                </span>
              </div>
            </div>
          </div>

          <!-- 图片滚动区域 -->
          <div
            v-if="record.images?.length"
            :ref="(el) => setRecordScrollViewRef(el, index)"
            class="record-images-scroll mb-2 overflow-x-auto"
            @scroll="handleScroll($event, index)"
          >
            <div class="flex">
              <div
                v-for="(img, imgIndex) in record.images" :key="imgIndex"
                class="record-image-item relative mr-3 h-32 w-[240px] flex-shrink-0 cursor-pointer overflow-hidden"
                @click="previewImages(record.images!, imgIndex)"
              >
                <!-- 使用懒加载图片 -->
                <img
                  v-if="isImageVisible(record, imgIndex)"
                  :src="getImageUrl(record, imgIndex)"
                  class="h-full w-full object-cover"
                  alt="训练记录图片"
                >
                <div v-else class="h-full w-full flex items-center justify-center bg-gray-100">
                  <div class="loading-placeholder" />
                </div>
                <!-- 不合格图标 -->
                <img :src="img.iconImg" class="absolute right-0 top-0 z-10 h-11 w-11" alt="状态图标">
                <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 px-2 py-1">
                  <div class="flex items-center justify-between">
                    <span class="text-xs text-white">
                      {{ img.description }}
                    </span>
                    <span class="text-[10px] text-white opacity-80">
                      {{ img.time }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 指示点 -->
          <div v-if="record.images && record.images.length > 1" class="mt-2 flex justify-center">
            <div
              v-for="n in record.images.length" :key="n"
              class="mx-0.5 h-1.5 w-1.5 cursor-pointer rounded-full"
              :class="n === (record.activeImgIndex || 1) ? 'bg-[#00996B]' : 'bg-[#EAEDF0]'"
              @click="handleIndicatorClick(index, n - 1)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.records-section {
  /* 时间轴相关样式 */
  .record-item {
    position: relative;
    margin-bottom: 0;

    &:last-child {
      margin-bottom: 0;

      .record-left-border {
        bottom: 0;
      }
    }
  }

  .record-content {
    position: relative;
  }

  .record-left-border {
    position: absolute;
    top: 42px; /* 转换rpx为px */
    bottom: -12px; /* 转换rpx为px */
    left: 16px; /* 转换rpx为px */
    width: 1px;
    background-color: #eaedf0;
    z-index: 9;
  }

  .record-dot {
    position: absolute;
    top: 22px; /* 转换rpx为px，调整与日期文本垂直对齐 */
    left: 16px; /* 转换rpx为px */
    width: 8px;
    height: 8px;
    border-radius: 50%;
    transform: translateX(-50%);
    z-index: 2;
  }

  .record-dot-active {
    background-color: #00996b;
  }

  .record-dot-inactive {
    background-color: white;
    border: 1px solid #d8d8d8;
  }

  .record-images-scroll {
    white-space: nowrap;
    margin-left: -2px;
    margin-right: -2px;
    padding: 2px;
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }
  }

  .record-image-item {
    display: inline-block;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    transition: transform 0.2s ease;
    border-radius: 8px; /* 添加圆角 */

    &:hover {
      transform: scale(1.02);
    }

    &:active {
      transform: scale(0.98);
    }
  }

  .loading-placeholder {
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #00996b;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }
}
</style>
