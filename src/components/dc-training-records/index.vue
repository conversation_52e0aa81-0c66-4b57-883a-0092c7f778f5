<template>
    <view class="records-section px-0 py-2 mb-8">
        <view class="section-title text-[36rpx] font-medium mb-2 px-4 text-[#333333]">{{ title }}</view>

        <!-- 训练记录列表 -->
        <view class="record-list">
            <!-- 记录项 -->
            <view v-for="(record, index) in records" :key="index" class="record-item bg-white ml-4 mr-4 relative">
                <!-- 记录内容 -->
                <view class="record-content pl-8 pr-4 py-4 relative">
                    <!-- 左侧线条和圆点 -->
                    <view class="record-left-border"></view>
                    <view class="record-dot" :class="index === 0 ? 'record-dot-active' : 'record-dot-inactive'">
                    </view>

                    <!-- 日期行 -->
                    <view class="flex justify-between mb-1">
                        <view class="flex items-center">
                            <text class="text-sm font-medium"
                                :class="index === 0 ? 'text-[#00996B]' : 'text-[#333333]'">{{ record.date }}</text>
                        </view>
                        <view>
                            <text class="text-sm text-[#999999]">作业次数：</text>
                            <text class="text-sm font-medium text-[#00996B]">{{ record.workCount }}次</text>
                        </view>
                    </view>

                    <!-- 数据统计网格 -->
                    <view class="mb-1">
                        <view class="flex justify-between mb-1">
                            <view class="flex">
                                <text class="text-xs text-[#999999]">作业合格率：</text>
                                <text class="text-xs font-medium text-[#333333]">{{ record.workPassRate }}%</text>
                            </view>
                            <view class="flex">
                                <text class="text-xs text-[#999999]">作业不合格次数：</text>
                                <text class="text-xs font-medium text-[#F53F3F]">{{ record.workFailCount }}次</text>
                            </view>
                        </view>
                        <view class="flex justify-between">
                            <view class="flex">
                                <text class="text-xs text-[#999999]">动作达标率：</text>
                                <text class="text-xs font-medium text-[#333333]">{{ record.actionRate }}%</text>
                            </view>
                            <view class="flex">
                                <text class="text-xs text-[#999999]">动作不达标次数：</text>
                                <text class="text-xs font-medium text-[#F53F3F]">{{ record.actionFailCount
                                    }}次</text>
                            </view>
                        </view>
                    </view>

                    <!-- 图片滚动区域 -->
                    <scroll-view class="record-images-scroll mb-2" scroll-x show-scrollbar="false"
                        :scroll-left="record.scrollLeft" :ref="(el) => setRecordScrollViewRef(el, index)"
                        @scroll="handleScroll($event, index)" v-if="record.images?.length">
                        <view class="flex">
                            <view v-for="(img, imgIndex) in record.images" :key="imgIndex"
                                class="record-image-item relative mr-3 w-[240px] h-32 overflow-hidden flex-shrink-0"
                                @click="previewImages(record.images, imgIndex)">
                                <!-- 使用懒加载图片 -->
                                <image v-if="isImageVisible(record, imgIndex)" :src="getImageUrl(record, imgIndex)"
                                    mode="aspectFill" class="w-full h-full"></image>
                                <view v-else class="w-full h-full bg-gray-100 flex items-center justify-center">
                                    <view class="loading-placeholder"></view>
                                </view>
                                <!-- 不合格图标 -->
                                <image :src="img.iconImg" class="absolute top-0 right-0 w-11 h-11 z-10">
                                </image>
                                <view class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 px-2 py-1">
                                    <view class="flex justify-between items-center">
                                        <text class="text-white text-xs">{{ img.description }}</text>
                                        <text class="text-white text-[10px] opacity-80">{{ img.time }}</text>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </scroll-view>

                    <!-- 指示点 -->
                    <view v-if="record.images && record.images.length > 1" class="flex justify-center mt-2">
                        <view v-for="n in record.images.length" :key="n" class="mx-0.5 w-1.5 h-1.5 rounded-full"
                            :class="n === (record.activeImgIndex || 1) ? 'bg-[#00996B]' : 'bg-[#EAEDF0]'"
                            @click="handleIndicatorClick(index, n - 1)"></view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup lang="ts">
import { ref, defineProps, nextTick, onMounted } from 'vue'
import { V1ManageTrainStudyRecordsRecordDetailAlarmUrlRecordDetailAlarmId } from '@/api/cddc.req'

// 定义训练记录数据接口
interface TrainingRecord {
    date: string
    workPassRate: string
    workCount: string
    workFailCount: string
    actionRate: string
    actionFailCount: string
    images?: {
        imageUrl: string
        description: string
        time: string
        iconImg: string
        isQualified?: boolean // 是否合格标识
        recordDetailAlarmId?: string // 图片ID，用于懒加载
        loaded?: boolean // 是否已加载
    }[]
    scrollLeft?: number
    activeImgIndex?: number
    visibleImageRange?: [number, number] // 可见图片范围 [startIndex, endIndex]
}

// 定义组件属性
const props = defineProps({
    // 标题
    title: {
        type: String,
        default: '训练记录'
    },
    // 训练记录数据
    records: {
        type: Array as () => TrainingRecord[],
        default: () => []
    }
})

// 滚动视图引用
const recordScrollViewRefs = ref<any[]>([])
function setRecordScrollViewRef(el, index) {
    if (el) {
        recordScrollViewRefs.value[index] = el
        // 初始化可见范围
        initVisibleRange(index)
    }
}

// 初始化可见图片范围
function initVisibleRange(recordIndex) {
    const record = props.records[recordIndex]
    if (!record || !record.images || record.images.length === 0) return

    // 默认第一张图片可见
    record.visibleImageRange = [0, Math.min(1, record.images.length - 1)]
    // 预加载第一张图片
    loadImageIfNeeded(record, 0)
}

// 处理滚动事件
function handleScroll(event, recordIndex) {
    const record = props.records[recordIndex]
    if (!record || !record.images || record.images.length <= 1) return

    // 获取当前滚动位置
    const scrollLeft = event.detail.scrollLeft
    const imageWidth = 240 // px
    const gap = 12 // mr-3的gap，单位px

    // 计算当前显示的图片索引
    const currentIndex = Math.round(scrollLeft / (imageWidth + gap))
    // 更新activeImgIndex
    if (record.activeImgIndex !== currentIndex + 1) {
        record.activeImgIndex = currentIndex + 1
    }

    // 更新可见图片范围 (当前图片及其前后各一张)
    const startIndex = Math.max(0, currentIndex - 1)
    const endIndex = Math.min(record.images.length - 1, currentIndex + 1)

    // 如果可见范围发生变化，才更新并触发加载
    if (!record.visibleImageRange ||
        record.visibleImageRange[0] !== startIndex ||
        record.visibleImageRange[1] !== endIndex) {

        // console.log(`记录${recordIndex}可见范围更新: ${startIndex}-${endIndex}`)
        record.visibleImageRange = [startIndex, endIndex]

        // 加载可见范围内的图片
        for (let i = startIndex; i <= endIndex; i++) {
            loadImageIfNeeded(record, i)
        }
    }
}

// 判断图片是否在可视范围内
function isImageVisible(record, imgIndex) {
    if (!record.visibleImageRange) return false
    const [start, end] = record.visibleImageRange
    return imgIndex >= start && imgIndex <= end
}

// 获取图片URL
function getImageUrl(record, imgIndex) {
    const img = record.images?.[imgIndex]
    if (!img) return '/static/def-bg.png'

    // 如果已经加载过，直接返回URL
    if (img.loaded && img.imageUrl) {
        return img.imageUrl
    }

    // 否则返回默认图片
    return '/static/def-bg.png'
}

// 单独的图片加载函数，返回Promise以便await
async function loadImageAsync(img) {
    if (!img || img.loaded) return img?.imageUrl || '/static/def-bg.png'

    let defImageUrl = '/static/def-bg.png';
    // 如果有recordDetailAlarmId但没有加载过图片
    if (img.recordDetailAlarmId && !img.loaded) {
        try {
            // console.log(`加载图片ID: ${img.recordDetailAlarmId}`) // 调试信息
            const imageResponse = await V1ManageTrainStudyRecordsRecordDetailAlarmUrlRecordDetailAlarmId({
                recordDetailAlarmId: img.recordDetailAlarmId
            })
            img.imageUrl = imageResponse || defImageUrl;
            img.loaded = true
            // console.log(`图片加载成功: ${img.imageUrl}`) // 调试信息
            return img.imageUrl
        } catch (error) {
            // console.error(`图片加载失败:`, error)
            img.imageUrl = defImageUrl
            img.loaded = true
            return defImageUrl
        }
    } else if (!img.recordDetailAlarmId) {
        // 没有ID的情况，标记为已加载，避免重复请求
        img.loaded = true
        return img.imageUrl || defImageUrl
    }
    return defImageUrl
}

// 按需加载图片
async function loadImageIfNeeded(record, imgIndex) {
    const img = record.images?.[imgIndex]
    if (!img || img.loaded) return
    let defImageUrl = '/static/def-bg.png';
    // 如果有recordDetailAlarmId但没有加载过图片
    if (img.recordDetailAlarmId && !img.loaded) {
        try {
            console.log(`加载图片: ${imgIndex}`) // 调试信息
            const imageResponse = await V1ManageTrainStudyRecordsRecordDetailAlarmUrlRecordDetailAlarmId({
                recordDetailAlarmId: img.recordDetailAlarmId
            })
            img.imageUrl = imageResponse || defImageUrl;
            img.loaded = true
            console.log(`图片${imgIndex}加载成功`) // 调试信息
        } catch (error) {
            console.error(`图片${imgIndex}加载失败:`, error)
            img.imageUrl = defImageUrl
            img.loaded = true
        }
    } else if (!img.recordDetailAlarmId) {
        // 没有ID的情况，标记为已加载，避免重复请求
        img.loaded = true
    }
}

// 处理指示点点击
function handleIndicatorClick(recordIndex, imgIndex) {
    const record = props.records[recordIndex]
    if (!record || !record.images || !record.images.length) return
    // 单张图片无需滚动
    if (record.images.length <= 1) return
    // 计算scrollLeft
    const imageWidth = 240 // px
    const gap = 12 // mr-3的gap，单位px
    const scrollLeft = imgIndex * (imageWidth + gap)
    record.scrollLeft = scrollLeft
    record.activeImgIndex = imgIndex + 1

    // 更新可见范围并加载图片
    const startIndex = Math.max(0, imgIndex - 1)
    const endIndex = Math.min(record.images.length - 1, imgIndex + 1)
    record.visibleImageRange = [startIndex, endIndex]

    // 加载可见范围内的图片
    for (let i = startIndex; i <= endIndex; i++) {
        loadImageIfNeeded(record, i)
    }

    // 滚动到对应图片
    nextTick(() => {
        const scrollView = recordScrollViewRefs.value[recordIndex]
        if (scrollView && scrollView.scrollTo) {
            scrollView.scrollTo({ left: scrollLeft, duration: 300, behavior: 'smooth' })
        }
    })
}

// 预览图片
const previewImages = async (images, currentIndex) => {
    // 显示加载中提示
    uni.showLoading({
        title: '加载图片中...',
        mask: true
    })

    try {
        // 预加载所有图片，确保在预览时所有图片都已加载
        const loadPromises = []

        // 收集所有需要加载的图片的Promise
        for (let i = 0; i < images.length; i++) {
            const img = images[i]
            if (img && img.recordDetailAlarmId && !img.loaded) {
                loadPromises.push(loadImageAsync(img))
            }
        }

        // 等待所有图片加载完成
        if (loadPromises.length > 0) {
            await Promise.all(loadPromises)
        }
        console.log('images', images)
        // 创建包含完整信息的数据结构
        const imageData = images.map(img => ({
            url: img.imageUrl || '/static/def-bg.png',
            description: img.description || '',
            time: img.time || '',
            iconImg: img.iconImg || '/static/icon-standard.png',
            isQualified: img.isQualified // 传递是否合格的标识
        }))

        // 隐藏加载提示
        uni.hideLoading()

        // 将图片信息保存到全局状态，以便在预览页面使用
        uni.setStorageSync('previewImageData', imageData)
        uni.setStorageSync('previewCurrentIndex', currentIndex)

        // 跳转到自定义预览页面
        uni.navigateTo({
            url: '/pages/image-preview/index?from=training-records',
            success: () => {
                // console.log('跳转到图片预览页面成功')
            },
            fail: (err) => {
                // 如果跳转失败，回退到使用系统预览
                const urls = imageData.map(item => item.url)
                uni.previewImage({
                    current: urls[currentIndex],
                    urls: urls,
                    indicator: 'number',
                    loop: true
                })
            }
        })
    } catch (error) {
        console.error('预览图片失败:', error)
        uni.hideLoading()
        uni.showToast({
            title: '图片加载失败',
            icon: 'none'
        })
    }
}

// 组件挂载时初始化所有记录的可见范围
onMounted(() => {
    if (props.records && props.records.length > 0) {
        console.log('初始化训练记录组件，记录数:', props.records.length)
        props.records.forEach((record, index) => {
            if (record.images && record.images.length > 0) {
                // 初始化可见范围为前两张图片
                record.visibleImageRange = [0, Math.min(1, record.images.length - 1)]
                record.activeImgIndex = 1 // 默认选中第一张

                // 确保可见范围内的图片已加载
                for (let i = 0; i <= Math.min(1, record.images.length - 1); i++) {
                    loadImageIfNeeded(record, i)
                }

                console.log(`记录${index}初始化完成，图片数:${record.images.length}`)
            }
        })
    }
})
</script>

<style lang="scss" scoped>
.records-section {

    /* 时间轴相关样式 */
    .record-item {
        position: relative;
        margin-bottom: 0;

        &:last-child {
            margin-bottom: 0;

            .record-left-border {
                bottom: 0;
            }
        }
    }

    .record-content {
        position: relative;
    }

    .record-left-border {
        position: absolute;
        top: 84rpx;
        bottom: -24rpx;
        left: 32rpx;
        width: 1px;
        background-color: #EAEDF0;
        z-index: 9;
    }

    .record-dot {
        position: absolute;
        top: 44rpx;
        /* 调整与日期文本垂直对齐 */
        left: 32rpx;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        transform: translateX(-50%);
        z-index: 2;
    }

    .record-dot-active {
        background-color: #00996B;
    }

    .record-dot-inactive {
        background-color: white;
        border: 1px solid #D8D8D8;
    }

    .record-images-scroll {
        white-space: nowrap;
        margin-left: -2px;
        margin-right: -2px;
        padding: 2px;
    }

    .record-image-item {
        display: inline-block;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
        transition: transform 0.2s ease;

        &:active {
            transform: scale(0.98);
        }
    }

    .loading-placeholder {
        width: 30px;
        height: 30px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #00996B;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }
}
</style>