<template>
  <div class="p-4">
    <h1 class="text-xl font-bold mb-4">训练记录组件测试</h1>
    <TrainingRecords :records="mockRecords" title="测试训练记录" />
  </div>
</template>

<script setup lang="ts">
import TrainingRecords from './TrainingRecords.vue'

// 模拟测试数据
const mockRecords = [
  {
    date: '2024-01-15',
    workPassRate: '95',
    workCount: '10',
    workFailCount: '1',
    actionRate: '88',
    actionFailCount: '2',
    images: [
      {
        imageUrl: 'https://via.placeholder.com/240x128/00996B/FFFFFF?text=Image1',
        description: '正常操作',
        time: '09:30',
        iconImg: 'https://via.placeholder.com/44x44/00FF00/FFFFFF?text=✓',
        isQualified: true,
        loaded: true,
      },
      {
        imageUrl: 'https://via.placeholder.com/240x128/F53F3F/FFFFFF?text=Image2',
        description: '违规操作',
        time: '10:15',
        iconImg: 'https://via.placeholder.com/44x44/FF0000/FFFFFF?text=✗',
        isQualified: false,
        loaded: true,
      },
      {
        imageUrl: 'https://via.placeholder.com/240x128/00996B/FFFFFF?text=Image3',
        description: '标准动作',
        time: '11:00',
        iconImg: 'https://via.placeholder.com/44x44/00FF00/FFFFFF?text=✓',
        isQualified: true,
        loaded: true,
      },
    ],
    scrollLeft: 0,
    activeImgIndex: 1,
    visibleImageRange: [0, 2] as [number, number],
  },
  {
    date: '2024-01-14',
    workPassRate: '92',
    workCount: '8',
    workFailCount: '1',
    actionRate: '85',
    actionFailCount: '3',
    images: [
      {
        imageUrl: 'https://via.placeholder.com/240x128/00996B/FFFFFF?text=Image4',
        description: '规范操作',
        time: '14:20',
        iconImg: 'https://via.placeholder.com/44x44/00FF00/FFFFFF?text=✓',
        isQualified: true,
        loaded: true,
      },
    ],
    scrollLeft: 0,
    activeImgIndex: 1,
    visibleImageRange: [0, 0] as [number, number],
  },
]
</script>
